// Copyright 2024 Diversion Company, Inc. All Rights Reserved.
/**
 * Diversion Core API
 * Definition of the Core API used to access low-level functionality of Diversion
 *
 * The version of the OpenAPI document: 0.2.0
 *
 * NOTE: This class is auto generated by OpenAPI-Generator 7.10.0.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

/*
 * AsyncResponse.h
 *
 * Response with the location of the result of an asynchronous processing of the request
 */
#pragma once


#include "ModelBase.h"

namespace Diversion {
namespace CoreAPI {
namespace Model {


/*
 * AsyncResponse
 *
 * Response with the location of the result of an asynchronous processing of the request
 */
class COREAPI_API AsyncResponse
    : public Model
{
public:
    virtual ~AsyncResponse() {}

	bool FromJson(const TSharedPtr<FJsonValue>& JsonValue) override;
	void WriteJson(JsonWriter& Writer) const override;


	FString mResult_url;

	int32_t mTimeout_sec = 0;

	int32_t mStatus = 0;

	FString mRef_id;

	TOptional<int32_t> mPoll_interval_sec;

};


}
}
}

