// Copyright 2023 EMPERIA-VR. All Rights Reserved.
#pragma once
#include "FInteractableLink.h"
#include "Data Structures/JSON/FPanoramaPosition.h"
#include "FVideoLink.generated.h"
USTRUCT()
struct FVideoLink : public FInteractableLink
{
	GENERATED_BODY()

	UPROPERTY()
	FPanoramaPosition Position;

	UPROPERTY()
	int Width;

	UPROPERTY()
	int Height;

	UPROPERTY()
	float ZRotation;

	UPROPERTY()
	float YRotation;

	FVideoLink(): Width(0), Height(0), ZRotation(0.0f), YRotation(0.0f)
	{
	}

	FVideoLink(const int Id, const FPanoramaPosition Position, const int Width, const int Height, const float ZRotation,
	           const float YRotation)
		: FInteractableLink(Id), Position(Position), Width(Width), Height(Height), ZRotation(ZRotation),
		  YRotation(YRotation)
	{
	}
};
