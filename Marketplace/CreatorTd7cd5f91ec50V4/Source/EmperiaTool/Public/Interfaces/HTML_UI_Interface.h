// Copyright 2023 EMPERIA-VR. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "HTML_UI_Interface.generated.h"


UINTERFACE(Blueprintable)
class UHTML_UI_Interface : public UInterface
{
	GENERATED_BODY()
};

/**
 * 
 */
class EMPERIATOOL_API IHTML_UI_Interface
{
	GENERATED_BODY()
public:
	UFUNCTION( BlueprintImplementableEvent, BlueprintCallable, Category = "LOGIN")
	void OnOTPReceived(bool IsSuccessfullyReceived);
	
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "LOGIN")
	void OnAuthTokenVerified(bool IsSuccessfullyReceived);
	
};
