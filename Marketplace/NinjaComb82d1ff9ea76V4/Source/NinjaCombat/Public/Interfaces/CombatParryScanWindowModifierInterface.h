// Ninja Bear Studio Inc., all rights reserved.
#pragma once

#include "CoreMinimal.h"
#include "NinjaCombatTags.h"
#include "UObject/Interface.h"
#include "CombatParryScanWindowModifierInterface.generated.h"

UINTERFACE(MinimalAPI)
class UCombatParryScanWindowModifierInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * Defines a specific length of a parry scan window.
 */
class NINJACOMBAT_API ICombatParryScanWindowModifierInterface
{
	
	GENERATED_BODY()

public:

	/**
	 * Checks if a given parry window is active.
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Parry Scan Window Modifier Interface")
	bool IsParryWindowActive(UPARAM(meta = (Categories = "Combat.Parry.Window")) FGameplayTag ParryWindowTag) const;
	virtual bool IsParryWindowActive_Implementation(const FGameplayTag ParryWindowTag) const
	{
		return ParryWindowTag == Tag_Combat_Parry_Window_Default;
	}
	
	/**
	 * Provides the duration for a parry scan window.
	 *
	 * This is used by the Parry Scan notify when a weapon or attacker is
	 * executing a parry attack. Bigger windows will make the parry easier.
	 *
	 * To modify a parry window, this function must return a value that is
	 * greater than zero. Otherwise, no modification is made.
	 */
	UFUNCTION(BlueprintCallable, BlueprintNativeEvent, Category = "Parry Scan Window Modifier Interface")
	float GetParryScanWindowLength(UPARAM(meta = (Categories = "Combat.Parry.Window")) FGameplayTag ParryWindow) const;
	virtual float GetParryScanWindowLength_Implementation(FGameplayTag ParryWindow) const
	{
		return 0.f;
	}
};