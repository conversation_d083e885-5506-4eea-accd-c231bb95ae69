//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////
///			Copyright 2024 (C) <PERSON> Xavier B. Leite
//////////////////////////////////////////////////////////////

#include "SPBSO_Speaker.h"
#include "SPBSO_Listener.h"
#include "SPBSO_Gate.h"

#include "Math/UnrealMathUtility.h"
#include "Runtime/Engine/Public/EngineUtils.h"

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

USPBSO_Speaker::USPBSO_Speaker(const FObjectInitializer &OBJ) : Super(OBJ) {
	Mobility = EComponentMobility::Movable;
	bWantsInitializeComponent = true;
	bEnableLowPassFilter = true;
	SetActive(true,false);
	bAutoActivate = true;
	bAutoRegister = true;
	//
	TEnumAsByte<ECollisionChannel> Static = ECollisionChannel::ECC_WorldStatic;
	TEnumAsByte<ECollisionChannel> Dynamic = ECollisionChannel::ECC_WorldDynamic;
	//
	TraceChannels.Empty();
	TraceChannels.Add(Static);
	TraceChannels.Add(Dynamic);
	AggregateOcclusion = false;
	TraceComplexGeometry = true;
	//
	PitchMultiplier = DefaultPitchMultiplier;
	VolumeMultiplier = DefaultVolumeMultiplier;
	LowPassFilterFrequency = DefaultLowPassFilter;
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

#if WITH_EDITORONLY_DATA
//
void USPBSO_Speaker::OnRegister() {
	Super::OnRegister();
	UpdateSprite();
}
//
void USPBSO_Speaker::UpdateSprite() {
	if (SpriteComponent) {
		SpriteComponent->SpriteInfo.Category = TEXT("Speaker");
		SpriteComponent->SpriteInfo.DisplayName = NSLOCTEXT("SpriteCategory","Speaker","Speaker");
		auto T_Speaker = LoadObject<UTexture2D>(nullptr,*FString::Printf(TEXT("/%s/%s"),PLUGIN_NAME,TEXT("Textures/T_Speaker.T_Speaker")));
		if (T_Speaker) {SpriteComponent->SetSprite(T_Speaker);}
	} else {
		SpriteComponent = NewObject<UBillboardComponent>(GetOwner(),NAME_None,RF_Transactional|RF_Transient|RF_TextExportTransient);
		SpriteComponent->Sprite = LoadObject<UTexture2D>(nullptr,*FString::Printf(TEXT("/%s/%s"),PLUGIN_NAME,TEXT("Textures/T_Speaker.T_Speaker")));
		SpriteComponent->SpriteInfo.DisplayName = NSLOCTEXT("SpriteCategory","Speaker","Speaker");
		SpriteComponent->SpriteInfo.Category = TEXT("Speaker");
		//
		SpriteComponent->SetRelativeScale3D(FVector(1.f,1.f,1.f));
		SpriteComponent->Mobility = EComponentMobility::Movable;
		SpriteComponent->CreationMethod = CreationMethod;
		SpriteComponent->AlwaysLoadOnClient = false;
		SpriteComponent->bIsScreenSizeScaled = true;
		SpriteComponent->bUseInEditorScaling = true;
		SpriteComponent->bIsEditorOnly = true;
		//
		SpriteComponent->SetupAttachment(this);
		SpriteComponent->RegisterComponent();
	}
}
//
#endif

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void USPBSO_Speaker::OnUnregister() {
	Super::OnUnregister();
}

void USPBSO_Speaker::PostLoad() {
	Super::PostLoad();
	//
#if UE_BUILD_SHIPPING
	OnAudioPlaybackPercentNative.AddUObject(this,&USPBSO_Speaker::SoundPlay__Fast);
#else
	OnAudioPlaybackPercentNative.AddUObject(this,&USPBSO_Speaker::SoundPlay);
#endif
	OnAudioFinishedNative.AddUObject(this,&USPBSO_Speaker::SoundFinished);
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void USPBSO_Speaker::BeginPlay() {
	Super::BeginPlay();
	//
	if (!IsActive()) {return;}
	DefaultAttenuation = AttenuationSettings;
	WantsAttenuationOverride = bOverrideAttenuation;
	//
	bAllowSpatialization = true;
	bEnableLowPassFilter = true;
	//
	if (GetAttenuationSettingsToApply()) {
		AttenuationOverrides.bAttenuate = true;
		AttenuationOverrides.bSpatialize = true;
		AttenuationOverrides.bEnableOcclusion = false;
		AttenuationOverrides.bAttenuateWithLPF = false;
		//
		if (AttenuationSettings) {
			AttenuationSettings->Attenuation.bAttenuate = true;
			AttenuationSettings->Attenuation.bSpatialize = true;
			AttenuationSettings->Attenuation.bEnableOcclusion = false;
			AttenuationSettings->Attenuation.bAttenuateWithLPF = false;
		}
		//
		if (Sound && Sound->AttenuationSettings) {
			Sound->AttenuationSettings->Attenuation.bAttenuate = true;
			Sound->AttenuationSettings->Attenuation.bSpatialize = true;
			Sound->AttenuationSettings->Attenuation.bEnableOcclusion = false;
			Sound->AttenuationSettings->Attenuation.bAttenuateWithLPF = false;
		}
	}
	//
	FetchGates();
	FetchSettings();
	FetchListeners();
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
/// SPB Ray-Tracing System:

void USPBSO_Speaker::SoundPlay(const class UAudioComponent* Component, const class USoundWave* SoundWave, const float Playback) {
	if (!IsActive()||!HasBegunPlay()||!GetOwner()||!GetOwner()->GetWorld()) {return;}
	auto Settings = GetMutableDefault<USPBSO_Settings>();
	//
	if (NoTrace) {return;}
	//
	Execute_OnSoundPlay(this,this,Playback);
	//
	//
	/// Data Setup:
	FVector SP_Location = GetComponentLocation();
	FVector CL_Location = SP_Location;
	FVector CR_Location = SP_Location;
	//
	if (CL.IsValid()) {CL_Location = CL->GetComponentLocation();}
	if (CR.IsValid()) {CR_Location = CR->GetComponentLocation();}
	//
	//
	FetchGates();
	FetchSettings();
	FetchListeners();
	//
	//
	/// Get Closest Listener:
	if (Listeners.Num()==0) {LOG_SPB(Debug,Debug,-1.f,FColor::Red,TEXT("There's no SPB Listeners in World.")); return;}
	TWeakObjectPtr<USPBSO_Listener>Listener = nullptr;
	//
	for (auto L : Listeners) {
		if (L.Get()==nullptr) {continue;}
		if (L.Get()->IsPlayerListener) {Listener = L.Get(); break;}
		else {
			const auto Focus = FVector::Distance(SP_Location,L.Get()->GetComponentLocation());
			const auto Distance = FVector::Distance(SP_Location,L.Get()->GetComponentLocation());
		if (Focus < Distance) {Listener = L.Get();}}
	} if (Listener==nullptr) {LOG_SPB(Debug,Debug,-1.f,FColor::Red,TEXT("There's no SPB Listener in World.")); return;}
	if (!IgnoredActors.Contains(Listener.Get()->GetOwner())) {IgnoredActors.Add(Listener.Get()->GetOwner());}
	//
	FVector LT_Location = Listener.Get()->GetComponentLocation();
	FVector LL_Location = SP_Location;
	FVector LR_Location = SP_Location;
	//
	if (Listener.Get()->LL.IsValid()) {LL_Location = Listener.Get()->LL->GetComponentLocation();}
	if (Listener.Get()->LR.IsValid()) {LR_Location = Listener.Get()->LR->GetComponentLocation();}
	//
	//
	if (Debug) {
		DrawSphere(Debug,GetWorld(),SP_Location,25,20,FColor::Cyan);
		if (CL.IsValid()) {
			DrawSphere(Debug,GetWorld(),CL_Location,15,10,FColor::Yellow);
		}
		if (CR.IsValid()) {
			DrawSphere(Debug,GetWorld(),CR_Location,15,10,FColor::Yellow);
		}
	}
	//
	//
	/// Check Attenuation Extents:
	const auto &AT_Settings = GetAttenuationSettingsToApply();
	if (AT_Settings && FVector::Distance(SP_Location,LT_Location)>(AT_Settings->AttenuationShapeExtents.Size()+AT_Settings->FalloffDistance)) {return;}
	//
	//
	/// Gate Test Setup:
	TWeakObjectPtr<USPBSO_Gate> Gate = nullptr;
	bool SP_Occluded = false;
	bool CL_Occluded = false;
	bool CR_Occluded = false;
	//
	/// Find Closest Gate:
	FCollisionQueryParams GateParameters(FName(TEXT("SPB-Gate")),TraceComplexGeometry,GetOwner());
	for (auto Actor : IgnoredActors) {GateParameters.AddIgnoredActor(Actor);}
	GateParameters.bTraceComplex = TraceComplexGeometry;
	GateParameters.bReturnPhysicalMaterial = false;
	GateParameters.bIgnoreTouches = true;
	//
	for (auto G : Gates) {
		if (!G.Get()) {continue;}
		const auto Focus = FVector::Distance(SP_Location,G.Get()->GetComponentLocation());
		const auto Distance = FVector::Distance(SP_Location,Listener.Get()->GetComponentLocation());
	if (Focus < Distance) {Gate = G.Get();}}
	//
	if (Gate!=nullptr) {
		FHitResult GATE_Hit;
		for (const auto Channel : TraceChannels) {
			if (RayTrace(Channel,GATE_Hit,SP_Location,LT_Location,GateParameters)) {SP_Occluded = true;}
			if (RayTrace(Channel,GATE_Hit,CL_Location,LL_Location,GateParameters)) {CL_Occluded = true;}
			if (RayTrace(Channel,GATE_Hit,CR_Location,LR_Location,GateParameters)) {CR_Occluded = true;}
			if (SP_Occluded && CL_Occluded && CR_Occluded) {break;}
		} DrawLine(Debug,GetWorld(),SP_Location,Gate->GetComponentLocation(),FColor::Blue);
	}
	//
	if (!Gate.IsStale() && (SP_Occluded && CL_Occluded && CR_Occluded)) {
		DrawLine(Debug,GetWorld(),SP_Location,Gate->GetComponentLocation(),FColor::Blue);
		LOG_SPB(Debug,false,-1.f,FColor::Cyan,FString(TEXT("SPB Gate link shot for: "))+GetName());
		const TWeakObjectPtr<USPBSO_Listener> _Listener  = Listener;
		const TWeakObjectPtr<USPBSO_Speaker> _Speaker = this;
		const TWeakObjectPtr<USPBSO_Gate> _Gate = Gate;
		Gate->TraceLink(_Gate,_Listener,_Speaker,TraceChannels);
		if (!AggregateOcclusion) {return;}
	}
	//
	//
	/// Test Occlusion Culling before Tracing:
	bool Culled = !GetOwner()->WasRecentlyRendered();
	if (TraceOcclusion && Culled) {
		SetVolumeMultiplier(FMath::Lerp<float>(VolumeMultiplier,AT_Defaults.VolumeMultiplier/2,0.01f));
		SetPitchMultiplier(FMath::Lerp<float>(PitchMultiplier,AT_Defaults.PitchMultiplier/1.5f,0.001f));
		SetLowPassFilterFrequency(FMath::Lerp<float>(LowPassFilterFrequency,AT_Defaults.LPFFrequency/2,0.1f));
		LOG_SPB(Debug,false,-1.f,FColor::White,FString::Printf(TEXT("[%s] Speaker Occluded"),*GetName())); return;
	} else if (TraceOcclusion && (VolumeMultiplier<(AT_Defaults.VolumeMultiplier-0.1f)||VolumeMultiplier>(AT_Defaults.VolumeMultiplier+0.1f))) {
		SetPitchMultiplier(FMath::Lerp<float>(PitchMultiplier,AT_Defaults.PitchMultiplier,0.001f));
		SetVolumeMultiplier(FMath::Lerp<float>(VolumeMultiplier,AT_Defaults.VolumeMultiplier,0.01f));
		SetLowPassFilterFrequency(FMath::Lerp<float>(LowPassFilterFrequency,AT_Defaults.LPFFrequency,0.1f));
	}
	//
	/// Trace Parameters Setup:
	FCollisionQueryParams TraceParameters(FName(TEXT("SPB-Trace")),TraceComplexGeometry,GetOwner());
	for (auto Actor : IgnoredActors) {TraceParameters.AddIgnoredActor(Actor);}
	TraceParameters.bTraceComplex = TraceComplexGeometry;
	TraceParameters.bReturnPhysicalMaterial = true;
	TraceParameters.bIgnoreTouches = true;
	//
	//
	float SPLTCLR = 1.f;
	//
	/// Tracer Data Setup:
	FSPBSO_Material* SPLT_Material = &SPB_Material;
	FSPBSO_Material* CLLL_Material = &SPB_Material;
	FSPBSO_Material* CRLR_Material = &SPB_Material;
	FHitResult SPLT_Hit, SPLL_Hit, SPLR_Hit;
	FHitResult CLLT_Hit, CLLL_Hit, CLLR_Hit;
	FHitResult CRLT_Hit, CRLL_Hit, CRLR_Hit;
	//
	bool SPLT_PartiallyOccluded = false;
	bool CLLL_PartiallyOccluded = false;
	bool CRLR_PartiallyOccluded = false;
	bool SPLT_Occluded = false, SPLL_Occluded = false, SPLR_Occluded = false;
	bool CLLT_Occluded = false, CLLL_Occluded = false, CLLR_Occluded = false;
	bool CRLT_Occluded = false, CRLL_Occluded = false, CRLR_Occluded = false;
	//
	/// Multiple Tracing Pass. Do traces for each target channel:
	for (const auto Channel : TraceChannels) {
		/// Trace 1: Speaker to Listener:
		if ((!SPLT_Occluded) && RayTrace(Channel,SPLT_Hit,SP_Location,LT_Location,TraceParameters)) {
			DrawLine(Debug,GetWorld(),SPLT_Hit.TraceStart,SPLT_Hit.ImpactPoint,FColor::Green);
			DrawLine(Debug,GetWorld(),SPLT_Hit.ImpactPoint,LT_Location,FColor::Red);
			SPLT_Occluded = true; SPLTCLR += 1;
			//
			const auto MAT = SPLT_Hit.PhysMaterial.Get();
			if (MAT && Settings->PhysicalMaterials.Find(MAT)) {SPLT_Material = &Settings->PhysicalMaterials.FindChecked(MAT);}
			DrawSphere(Debug,GetWorld(),SPLT_Hit.ImpactPoint,SPLT_Material->WaveReflectionRadius,12,FColor::Green);
			//
			/// Trace Reflections:
			FHitResult REF_LastHit = SPLT_Hit;
			FVector REF_Location = (REF_LastHit.Normal+REF_LastHit.ImpactPoint);
			//
			for (int32 I=0; I<SPLT_Material->WaveReflectionIndex; I++) {
				const FVector Reflection = FMath::GetReflectionVector(REF_Location,REF_LastHit.ImpactNormal);
				//
				const FVector Refraction = Reflection.RotateAngleAxis((180.f-SPLT_Material->SurfaceDensity),FVector::ForwardVector);
				const FVector Scatter = ((Refraction+Reflection)/2);
				//
				FHitResult REFL_Hit;
				if (RayTrace(Channel,REFL_Hit,REF_Location,Reflection,TraceParameters)) {
					DrawSphere(Debug,GetWorld(),REFL_Hit.ImpactPoint,SPLT_Material->WaveReflectionRadius,12,FColor::Orange);
					DrawLine(Debug,GetWorld(),REFL_Hit.TraceStart,REFL_Hit.ImpactPoint,FColor::Orange);
					//
					if (FVector::Distance(REFL_Hit.ImpactPoint,LT_Location)<=SPLT_Material->WaveReflectionRadius) {
						SPLT_PartiallyOccluded = true; SPLT_Occluded = false; SPLTCLR += 1;
					}
					//
					REF_LastHit = REFL_Hit;
					REF_Location = (REF_LastHit.Normal+REF_LastHit.ImpactPoint);
				}
				//
				FHitResult REFR_Hit;
				if (RayTrace(Channel,REFR_Hit,REF_LastHit.ImpactPoint,Refraction,TraceParameters)) {
					DrawSphere(Debug,GetWorld(),REFR_Hit.ImpactPoint,SPLT_Material->WaveReflectionRadius,12,FColor::White);
					//
					if (FVector::Distance(REFR_Hit.ImpactPoint,LT_Location)<=SPLT_Material->WaveDiffractionRadius) {
						SPLT_PartiallyOccluded = true; SPLT_Occluded = false; SPLTCLR += 1;
					}
				}
				//
				if ((SPLT_Material->WaveScatteringRadius>0) && FVector::Distance(Scatter,LR_Location)<=SPLT_Material->WaveScatteringRadius) {
					DrawSphere(Debug,GetWorld(),SPLT_Hit.ImpactPoint,SPLT_Material->WaveScatteringRadius,12,FColor::Black);
					SPLT_PartiallyOccluded = true; SPLT_Occluded = false;
				}
			}
		}
		/// Trace 2: Speaker to LL:
		if ((!SPLL_Occluded) && Listener.Get()->LL.IsValid() && RayTrace(Channel,SPLL_Hit,SP_Location,LL_Location,TraceParameters)) {
			SPLTCLR += 1; DrawLine(Debug,GetWorld(),SPLL_Hit.TraceStart,SPLL_Hit.ImpactPoint,FColor::Green);
			if (SPLL_Hit.HitObjectHandle.FetchActor() != Listener.Get()->GetOwner()) {
				DrawLine(Debug,GetWorld(),SPLL_Hit.ImpactPoint,LL_Location,FColor::Red);
		} SPLL_Occluded = true;}
		/// Trace 3: Speaker to LR:
		if ((!SPLR_Occluded) && Listener.Get()->LR.IsValid() && RayTrace(Channel,SPLR_Hit,SP_Location,LR_Location,TraceParameters)) {
			SPLTCLR += 1; DrawLine(Debug,GetWorld(),SPLR_Hit.TraceStart,SPLR_Hit.ImpactPoint,FColor::Green);
			if (SPLR_Hit.HitObjectHandle.FetchActor() != Listener.Get()->GetOwner()) {
				DrawLine(Debug,GetWorld(),SPLR_Hit.ImpactPoint,LR_Location,FColor::Red);
		} SPLR_Occluded = true;}
		/// Trace 4: CL to Listener:
		if ((!CLLT_Occluded) && CL.IsValid() && RayTrace(Channel,CLLT_Hit,CL_Location,LT_Location,TraceParameters)) {
			SPLTCLR += 1; DrawLine(Debug,GetWorld(),CLLT_Hit.TraceStart,CLLT_Hit.ImpactPoint,FColor::Green);
			if (CLLT_Hit.HitObjectHandle.FetchActor() != Listener.Get()->GetOwner()) {
				DrawLine(Debug,GetWorld(),CLLT_Hit.ImpactPoint,LT_Location,FColor::Red);
		} CLLT_Occluded = true;}
		/// Trace 5: CL to LL:
		if ((!CLLL_Occluded) && CL.IsValid() && Listener.Get()->LL.IsValid() && RayTrace(Channel,CLLL_Hit,CL_Location,LL_Location,TraceParameters)) {
			DrawLine(Debug,GetWorld(),CLLL_Hit.TraceStart,CLLL_Hit.ImpactPoint,FColor::Green);
			DrawLine(Debug,GetWorld(),CLLL_Hit.ImpactPoint,LL_Location,FColor::Red);
			CLLL_Occluded = true; SPLTCLR += 1;
			//
			const auto MAT = CLLL_Hit.PhysMaterial.Get();
			if (MAT && Settings->PhysicalMaterials.Find(MAT)) {CLLL_Material = &Settings->PhysicalMaterials.FindChecked(MAT);}
			//
			if (FVector::Distance(CLLL_Hit.ImpactPoint,LL_Location)<=CLLL_Material->WaveReflectionRadius) {
				CLLL_PartiallyOccluded = true;
				CLLL_Occluded = false;
			}
		}
		/// Trace 6: CL to LR:
		if ((!CLLR_Occluded) && CL.IsValid() && Listener.Get()->LR.IsValid() && RayTrace(Channel,CLLR_Hit,CL_Location,LR_Location,TraceParameters)) {
			SPLTCLR += 1; DrawLine(Debug,GetWorld(),CLLR_Hit.TraceStart,CLLR_Hit.ImpactPoint,FColor::Green);
			if (CLLR_Hit.HitObjectHandle.FetchActor() != Listener.Get()->GetOwner()) {
				DrawLine(Debug,GetWorld(),CLLR_Hit.ImpactPoint,LR_Location,FColor::Red);
		} CLLR_Occluded = true;}
		/// Trace 7: CR to Listener:
		if ((!CRLT_Occluded) && CR.IsValid() && RayTrace(Channel,CRLT_Hit,CR_Location,LT_Location,TraceParameters)) {
			SPLTCLR += 1; DrawLine(Debug,GetWorld(),CRLT_Hit.TraceStart,CRLT_Hit.ImpactPoint,FColor::Green);
			if (CRLT_Hit.HitObjectHandle.FetchActor() != Listener.Get()->GetOwner()) {
				DrawLine(Debug,GetWorld(),CRLT_Hit.ImpactPoint,LT_Location,FColor::Red);
		} CRLT_Occluded = true;}
		/// Trace 8: CR to LL:
		if ((!CRLL_Occluded) && CR.IsValid() && Listener.Get()->LL.IsValid() && RayTrace(Channel,CRLL_Hit,CR_Location,LL_Location,TraceParameters)) {
			SPLTCLR += 1; DrawLine(Debug,GetWorld(),CRLL_Hit.TraceStart,CRLL_Hit.ImpactPoint,FColor::Green);
			if (CRLL_Hit.HitObjectHandle.FetchActor() != Listener.Get()->GetOwner()) {
				DrawLine(Debug,GetWorld(),CRLL_Hit.ImpactPoint,LL_Location,FColor::Red);
		} CRLL_Occluded = true;}
		/// Trace 9: CR to LR:
		if ((!CRLR_Occluded) && CR.IsValid() && Listener.Get()->LR.IsValid() && RayTrace(Channel,CRLR_Hit,CR_Location,LR_Location,TraceParameters)) {
			DrawLine(Debug,GetWorld(),CRLR_Hit.TraceStart,CRLR_Hit.ImpactPoint,FColor::Green);
			DrawLine(Debug,GetWorld(),CRLR_Hit.ImpactPoint,LR_Location,FColor::Red);
			CRLR_Occluded = true; SPLTCLR += 1;
			//
			const auto MAT = CRLR_Hit.PhysMaterial.Get();
			if (MAT && Settings->PhysicalMaterials.Find(MAT)) {CRLR_Material = &Settings->PhysicalMaterials.FindChecked(MAT);}
			//
			if (FVector::Distance(CRLR_Hit.ImpactPoint,LR_Location)<=CRLR_Material->WaveReflectionRadius) {
				CRLR_PartiallyOccluded = true;
				CRLR_Occluded = false;
			}
		}
	}
	//
	//
	/// Store Sound Parameters for each Trace:
	float SPLT_LPFFrequency = AT_Defaults.LPFFrequency;
	float SPLL_LPFFrequency = AT_Defaults.LPFFrequency;
	float SPLR_LPFFrequency = AT_Defaults.LPFFrequency;
	float CLLT_LPFFrequency = AT_Defaults.LPFFrequency;
	float CLLL_LPFFrequency = AT_Defaults.LPFFrequency;
	float CLLR_LPFFrequency = AT_Defaults.LPFFrequency;
	float CRLT_LPFFrequency = AT_Defaults.LPFFrequency;
	float CRLL_LPFFrequency = AT_Defaults.LPFFrequency;
	float CRLR_LPFFrequency = AT_Defaults.LPFFrequency;
	float SPLT_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float SPLL_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float SPLR_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CLLT_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CLLL_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CLLR_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CRLT_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CRLL_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CRLR_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float SPLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float SPLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float SPLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CLLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CLLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CLLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CRLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CRLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CRLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	//
	//
	/// Apply SP to LT:
	if (SPLT_PartiallyOccluded) {
		SPLT_LPFFrequency = SPLT_Material->LPFFrequency;
		SPLT_PitchMultiplier = AT_Defaults.PitchMultiplier-0.2f;
		SPLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/10.f;
	} else if (SPLT_Occluded) {
		SPLT_LPFFrequency = SPLT_Material->LPFFrequency/SPLTCLR;
		SPLT_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		SPLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else {
		DrawLine(Debug,GetWorld(),SP_Location,LT_Location,FColor::Green,false,-1.f,0,0.2f);
		DrawCircle(Debug,GetWorld(),LT_Location,12.f,8,FColor::Green,false,-1.f,1,0.2f,Listener.Get()->GetRightVector(),Listener.Get()->GetUpVector());
	}
	//
	/// Apply SP to LL:
	if (SPLL_Occluded) {
		SPLL_LPFFrequency = SPLT_Material->LPFFrequency/SPLTCLR;
		SPLL_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		SPLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else {DrawLine(Debug,GetWorld(),SP_Location,LL_Location,FColor::Cyan,false,-1.f,0,0.2f);}
	//
	/// Apply SP to LR:
	if (SPLR_Occluded) {
		SPLR_LPFFrequency = SPLT_Material->LPFFrequency/SPLTCLR;
		SPLR_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		SPLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else {DrawLine(Debug,GetWorld(),SP_Location,LR_Location,FColor::Cyan,false,-1.f,0,0.2f);}
	//
	/// Apply CL to LT:
	if (CLLT_Occluded) {
		CLLT_LPFFrequency = CLLL_Material->LPFFrequency/SPLTCLR;
		CLLT_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CLLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else if (CL.IsValid()) {DrawLine(Debug,GetWorld(),CL_Location,LT_Location,FColor::Cyan,false,-1.f,0,0.2f);}
	//
	/// Apply CL to LL:
	if (CLLL_PartiallyOccluded) {
		CLLL_LPFFrequency = CLLL_Material->LPFFrequency;
		CLLL_PitchMultiplier = AT_Defaults.PitchMultiplier-0.1f;
		CLLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/10.f;
	} else if (CLLL_Occluded) {
		CLLL_LPFFrequency = CLLL_Material->LPFFrequency/SPLTCLR;
		CLLL_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CLLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else if (CL.IsValid() && Listener.Get()->LL.IsValid()) {
		DrawLine(Debug,GetWorld(),CL_Location,LL_Location,FColor::Cyan,false,-1.f,0,0.2f);
		DrawCircle(Debug,GetWorld(),LL_Location,6.f,8,FColor::Cyan,false,-1.f,1,0.2f,Listener.Get()->LL->GetRightVector(),Listener.Get()->LL->GetUpVector());
	}
	//
	/// Apply CL to LR:
	if (CLLR_Occluded) {
		CLLR_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CLLR_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CLLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else if (CL.IsValid() && Listener.Get()->LR.IsValid()) {DrawLine(Debug,GetWorld(),CL_Location,LR_Location,FColor::Cyan,false,-1.f,0,0.2f);}
	//
	/// Apply CR to LT:
	if (CRLT_Occluded) {
		CRLT_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CRLT_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CRLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else if (CR.IsValid()) {DrawLine(Debug,GetWorld(),CR_Location,LT_Location,FColor::Cyan,false,-1.f,0,0.2f);}
	//
	/// Apply CR to LL:
	if (CRLL_Occluded) {
		CRLL_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CRLL_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CRLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else if (CR.IsValid() && Listener.Get()->LL.IsValid()) {DrawLine(Debug,GetWorld(),CR_Location,LL_Location,FColor::Cyan,false,-1.f,0,0.2f);}
	//
	/// Apply CR to LR:
	if (CRLR_PartiallyOccluded) {
		CRLR_LPFFrequency = CRLR_Material->LPFFrequency;
		CRLR_PitchMultiplier = AT_Defaults.PitchMultiplier-0.1f;
		CRLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/10.f;
	} else if (CRLR_Occluded) {
		CRLR_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CRLR_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CRLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	} else if (CR.IsValid() && Listener.Get()->LR.IsValid()) {
		DrawLine(Debug,GetWorld(),CR_Location,LR_Location,FColor::Cyan,false,-1.f,0,0.2f);
		DrawCircle(Debug,GetWorld(),LR_Location,6.f,8,FColor::Cyan,false,-1.f,1,0.2f,Listener.Get()->LR->GetRightVector(),Listener.Get()->LR->GetUpVector());
	}
	//
	//
	/// Calculate Averages:
	float __LPFFrequency = ((
		SPLT_LPFFrequency + SPLL_LPFFrequency + SPLR_LPFFrequency +
		CLLT_LPFFrequency + CLLL_LPFFrequency + CLLR_LPFFrequency +
		CRLT_LPFFrequency + CRLL_LPFFrequency + CRLR_LPFFrequency
	) / SPLTCLR) - (SPLT_Material->SurfaceTemperature + SPLT_Material->SurfaceDensity);
	//
	float _LPFFrequency = ((
		SPLT_Material->LPFFrequency +
		CLLL_Material->LPFFrequency +
		CRLR_Material->LPFFrequency
	) / SPLT_Material->SurfaceDensity) -
	((
		SPLT_Material->SurfaceDensity +
		CLLL_Material->SurfaceDensity +
		CRLR_Material->SurfaceDensity
	) / (SPLTCLR));
	//
	float _VolumeMultiplier = ((
		SPLT_VolumeMultiplier + SPLL_VolumeMultiplier + SPLR_VolumeMultiplier +
		CLLT_VolumeMultiplier + CLLL_VolumeMultiplier + CLLR_VolumeMultiplier +
		CRLT_VolumeMultiplier + CRLL_VolumeMultiplier + CRLR_VolumeMultiplier
	) / SPLTCLR);
	//
	float _PitchMultiplier = ((
		(SPLT_PitchMultiplier + SPLL_PitchMultiplier + SPLR_PitchMultiplier +
		CLLT_PitchMultiplier + CLLL_PitchMultiplier + CLLR_PitchMultiplier +
		CRLT_PitchMultiplier + CRLL_PitchMultiplier + CRLR_PitchMultiplier)
	) / SPLTCLR);
	//
	//
	AT_Attenuation.PitchMultiplier = FMath::Clamp<float>((_PitchMultiplier/SPLTCLR),AT_Defaults.PitchMultiplier-0.020f,AT_Defaults.PitchMultiplier);
	AT_Attenuation.LPFFrequency = FMath::Clamp<float>(((__LPFFrequency+_LPFFrequency)/2),20.f,20000.f);
	//
	if (SPLT_PartiallyOccluded||CLLL_PartiallyOccluded||CRLR_PartiallyOccluded) {
		AT_Attenuation.VolumeMultiplier = FMath::Clamp<float>(_VolumeMultiplier,0.001f,AT_Defaults.VolumeMultiplier);
		if (AT_Attenuation.VolumeMultiplier<0.001f) {AT_Attenuation.VolumeMultiplier=0.001f;}
	} else {
		AT_Attenuation.VolumeMultiplier = FMath::Clamp<float>((_VolumeMultiplier/SPLTCLR),0.001f,AT_Defaults.VolumeMultiplier);
		if (AT_Attenuation.VolumeMultiplier<0.001f) {AT_Attenuation.VolumeMultiplier=0.001f;}
	}
	//
	//
	SetPitchMultiplier(FMath::Lerp<float>(PitchMultiplier,AT_Attenuation.PitchMultiplier,0.001f));
	SetLowPassFilterFrequency(FMath::Lerp<float>(LowPassFilterFrequency,AT_Attenuation.LPFFrequency,SPLT_Material->WaveSignalInterpolation));
	//
	VolumeMultiplier = FMath::Lerp<float>(VolumeMultiplier,AT_Attenuation.VolumeMultiplier,SPLT_Material->WaveSignalInterpolation);
	if (VolumeMultiplier<0.001f) {VolumeMultiplier=0.001f;} SetVolumeMultiplier(VolumeMultiplier);
	//
	if (AT_Attenuation.VolumeMultiplier > 0.1f){Listener.Get()->OnListenOnce(Listener.Get(),this);}
	if (AT_Attenuation.VolumeMultiplier > 0.1f) {Listener.Get()->OnListen(Listener.Get(),this);}
	//
	//
	if (Debug) {
		LOG_SPB(Debug,false,-1.f,FColor::White,FString::Printf(TEXT("[%s] :: Volume Multiplier: %f"),*GetName(),AT_Attenuation.VolumeMultiplier));
		LOG_SPB(Debug,false,-1.f,FColor::White,FString::Printf(TEXT("[%s] :: Pitch Multiplier: %f"),*GetName(),AT_Attenuation.PitchMultiplier));
		LOG_SPB(Debug,false,-1.f,FColor::White,FString::Printf(TEXT("[%s] :: LPF Frequency: %f"),*GetName(),AT_Attenuation.LPFFrequency));
		LOG_SPB(Debug,false,-1.f,FColor::Purple,FString::Printf(TEXT("[%s]"),*Listener.Get()->GetName()));
		if (GEngine) {GEngine->AddOnScreenDebugMessage(-1,-1.f,FColor::White,TEXT(" "));}
	}
}

void USPBSO_Speaker::SoundPlay__Fast(const UAudioComponent* Component, const USoundWave* SoundWave, const float Playback) {
if (!IsActive()||!HasBegunPlay()||!GetOwner()||!GetOwner()->GetWorld()) {return;}
	auto Settings = GetMutableDefault<USPBSO_Settings>();
	//
	if (NoTrace) {return;}
	//
	Execute_OnSoundPlay(this,this,Playback);
	//
	//
	/// Data Setup:
	FVector SP_Location = GetComponentLocation();
	FVector CL_Location = SP_Location;
	FVector CR_Location = SP_Location;
	//
	if (CL.IsValid()) {CL_Location = CL->GetComponentLocation();}
	if (CR.IsValid()) {CR_Location = CR->GetComponentLocation();}
	//
	//
	FetchGates();
	FetchSettings();
	FetchListeners();
	//
	//
	/// Get Closest Listener:
	if (Listeners.Num()==0) {LOG_SPB(Debug,Debug,-1.f,FColor::Red,TEXT("There's no SPB Listeners in World.")); return;}
	TWeakObjectPtr<USPBSO_Listener>Listener = nullptr;
	//
	for (auto L : Listeners) {
		if (L.Get()==nullptr) {continue;}
		if (L.Get()->IsPlayerListener) {Listener = L.Get(); break;}
		else {
			const auto Focus = FVector::Distance(SP_Location,L.Get()->GetComponentLocation());
			const auto Distance = FVector::Distance(SP_Location,L.Get()->GetComponentLocation());
		if (Focus < Distance) {Listener = L.Get();}}
	} if (Listener==nullptr) {LOG_SPB(Debug,Debug,-1.f,FColor::Red,TEXT("There's no SPB Listener in World.")); return;}
	if (!IgnoredActors.Contains(Listener.Get()->GetOwner())) {IgnoredActors.Add(Listener.Get()->GetOwner());}
	//
	FVector LT_Location = Listener.Get()->GetComponentLocation();
	FVector LL_Location = SP_Location;
	FVector LR_Location = SP_Location;
	//
	if (Listener.Get()->LL.IsValid()) {LL_Location = Listener.Get()->LL->GetComponentLocation();}
	if (Listener.Get()->LR.IsValid()) {LR_Location = Listener.Get()->LR->GetComponentLocation();}
	//
	//
	/// Check Attenuation Extents:
	const auto &AT_Settings = GetAttenuationSettingsToApply();
	if (AT_Settings && FVector::Distance(SP_Location,LT_Location)>(AT_Settings->AttenuationShapeExtents.Size()+AT_Settings->FalloffDistance)) {return;}
	//
	//
	/// Gate Test Setup:
	TWeakObjectPtr<USPBSO_Gate> Gate = nullptr;
	bool SP_Occluded = false;
	bool CL_Occluded = false;
	bool CR_Occluded = false;
	//
	/// Find Closest Gate:
	FCollisionQueryParams GateParameters(FName(TEXT("SPB-Gate")),TraceComplexGeometry,GetOwner());
	for (auto Actor : IgnoredActors) {GateParameters.AddIgnoredActor(Actor);}
	GateParameters.bTraceComplex = TraceComplexGeometry;
	GateParameters.bReturnPhysicalMaterial = false;
	GateParameters.bIgnoreTouches = true;
	//
	for (auto G : Gates) {
		if (!G.Get()) {continue;}
		const auto Focus = FVector::Distance(SP_Location,G.Get()->GetComponentLocation());
		const auto Distance = FVector::Distance(SP_Location,Listener.Get()->GetComponentLocation());
	if (Focus < Distance) {Gate = G.Get();}}
	//
	if (Gate!=nullptr) {
		FHitResult GATE_Hit;
		for (const auto Channel : TraceChannels) {
			if (RayTrace(Channel,GATE_Hit,SP_Location,LT_Location,GateParameters)) {SP_Occluded = true;}
			if (RayTrace(Channel,GATE_Hit,CL_Location,LL_Location,GateParameters)) {CL_Occluded = true;}
			if (RayTrace(Channel,GATE_Hit,CR_Location,LR_Location,GateParameters)) {CR_Occluded = true;}
			if (SP_Occluded && CL_Occluded && CR_Occluded) {break;}
		}
	}
	//
	if (!Gate.IsStale() && (SP_Occluded && CL_Occluded && CR_Occluded)) {
		const TWeakObjectPtr<USPBSO_Listener> _Listener  = Listener;
		const TWeakObjectPtr<USPBSO_Speaker> _Speaker = this;
		const TWeakObjectPtr<USPBSO_Gate> _Gate = Gate;
		Gate->TraceLink(_Gate,_Listener,_Speaker,TraceChannels);
		if (!AggregateOcclusion) {return;}
	}
	//
	//
	/// Test Occlusion Culling before Tracing:
	bool Culled = !GetOwner()->WasRecentlyRendered();
	if (TraceOcclusion && Culled) {
		SetVolumeMultiplier(FMath::Lerp<float>(VolumeMultiplier,AT_Defaults.VolumeMultiplier/2,0.01f));
		SetPitchMultiplier(FMath::Lerp<float>(PitchMultiplier,AT_Defaults.PitchMultiplier/1.5f,0.001f));
		SetLowPassFilterFrequency(FMath::Lerp<float>(LowPassFilterFrequency,AT_Defaults.LPFFrequency/2,0.1f));
		LOG_SPB(Debug,false,-1.f,FColor::White,FString::Printf(TEXT("[%s] Speaker Occluded"),*GetName())); return;
	} else if (TraceOcclusion && (VolumeMultiplier<(AT_Defaults.VolumeMultiplier-0.1f)||VolumeMultiplier>(AT_Defaults.VolumeMultiplier+0.1f))) {
		SetPitchMultiplier(FMath::Lerp<float>(PitchMultiplier,AT_Defaults.PitchMultiplier,0.001f));
		SetVolumeMultiplier(FMath::Lerp<float>(VolumeMultiplier,AT_Defaults.VolumeMultiplier,0.01f));
		SetLowPassFilterFrequency(FMath::Lerp<float>(LowPassFilterFrequency,AT_Defaults.LPFFrequency,0.1f));
	}
	//
	/// Trace Parameters Setup:
	FCollisionQueryParams TraceParameters(FName(TEXT("SPB-Trace")),TraceComplexGeometry,GetOwner());
	for (auto Actor : IgnoredActors) {TraceParameters.AddIgnoredActor(Actor);}
	TraceParameters.bTraceComplex = TraceComplexGeometry;
	TraceParameters.bReturnPhysicalMaterial = true;
	TraceParameters.bIgnoreTouches = true;
	//
	//
	float SPLTCLR = 1.f;
	//
	/// Tracer Data Setup:
	FSPBSO_Material* SPLT_Material = &SPB_Material;
	FSPBSO_Material* CLLL_Material = &SPB_Material;
	FSPBSO_Material* CRLR_Material = &SPB_Material;
	FHitResult SPLT_Hit, SPLL_Hit, SPLR_Hit;
	FHitResult CLLT_Hit, CLLL_Hit, CLLR_Hit;
	FHitResult CRLT_Hit, CRLL_Hit, CRLR_Hit;
	//
	bool SPLT_PartiallyOccluded = false;
	bool CLLL_PartiallyOccluded = false;
	bool CRLR_PartiallyOccluded = false;
	bool SPLT_Occluded = false, SPLL_Occluded = false, SPLR_Occluded = false;
	bool CLLT_Occluded = false, CLLL_Occluded = false, CLLR_Occluded = false;
	bool CRLT_Occluded = false, CRLL_Occluded = false, CRLR_Occluded = false;
	//
	/// Multiple Tracing Pass. Do traces for each target channel:
	for (const auto Channel : TraceChannels) {
		/// Trace 1: Speaker to Listener:
		if ((!SPLT_Occluded) && RayTrace(Channel,SPLT_Hit,SP_Location,LT_Location,TraceParameters)) {
			SPLT_Occluded = true; SPLTCLR += 1;
			//
			const auto MAT = SPLT_Hit.PhysMaterial.Get();
			if (MAT && Settings->PhysicalMaterials.Find(MAT)) {SPLT_Material = &Settings->PhysicalMaterials.FindChecked(MAT);}
			//
			/// Trace Reflections:
			FHitResult REF_LastHit = SPLT_Hit;
			FVector REF_Location = (REF_LastHit.Normal+REF_LastHit.ImpactPoint);
			//
			for (int32 I=0; I<SPLT_Material->WaveReflectionIndex; I++) {
				const FVector Reflection = FMath::GetReflectionVector(REF_Location,REF_LastHit.ImpactNormal);
				//
				const FVector Refraction = Reflection.RotateAngleAxis((180.f-SPLT_Material->SurfaceDensity),FVector::ForwardVector);
				const FVector Scatter = ((Refraction+Reflection)/2);
				//
				FHitResult REFL_Hit;
				if (RayTrace(Channel,REFL_Hit,REF_Location,Reflection,TraceParameters)) {
					if (FVector::Distance(REFL_Hit.ImpactPoint,LT_Location)<=SPLT_Material->WaveReflectionRadius) {
						SPLT_PartiallyOccluded = true; SPLT_Occluded = false; SPLTCLR += 1;
					}
					//
					REF_LastHit = REFL_Hit;
					REF_Location = (REF_LastHit.Normal+REF_LastHit.ImpactPoint);
				}
				//
				FHitResult REFR_Hit;
				if (RayTrace(Channel,REFR_Hit,REF_LastHit.ImpactPoint,Refraction,TraceParameters)) {
					if (FVector::Distance(REFR_Hit.ImpactPoint,LT_Location)<=SPLT_Material->WaveDiffractionRadius) {
						SPLT_PartiallyOccluded = true; SPLT_Occluded = false; SPLTCLR += 1;
					}
				}
				//
				if ((SPLT_Material->WaveScatteringRadius>0) && FVector::Distance(Scatter,LR_Location)<=SPLT_Material->WaveScatteringRadius) {
					SPLT_PartiallyOccluded = true; SPLT_Occluded = false;
				}
			}
		}
		/// Trace 2: Speaker to LL:
		if ((!SPLL_Occluded) && Listener.Get()->LL.IsValid() && RayTrace(Channel,SPLL_Hit,SP_Location,LL_Location,TraceParameters)) {SPLTCLR += 1; SPLL_Occluded = true;}
		/// Trace 3: Speaker to LR:
		if ((!SPLR_Occluded) && Listener.Get()->LR.IsValid() && RayTrace(Channel,SPLR_Hit,SP_Location,LR_Location,TraceParameters)) {SPLTCLR += 1; SPLR_Occluded = true;}
		/// Trace 4: CL to Listener:
		if ((!CLLT_Occluded) && CL.IsValid() && RayTrace(Channel,CLLT_Hit,CL_Location,LT_Location,TraceParameters)) {
			SPLTCLR += 1; CLLT_Occluded = true;}
		/// Trace 5: CL to LL:
		if ((!CLLL_Occluded) && CL.IsValid() && Listener.Get()->LL.IsValid() && RayTrace(Channel,CLLL_Hit,CL_Location,LL_Location,TraceParameters)) {
			CLLL_Occluded = true; SPLTCLR += 1;
			//
			const auto MAT = CLLL_Hit.PhysMaterial.Get();
			if (MAT && Settings->PhysicalMaterials.Find(MAT)) {CLLL_Material = &Settings->PhysicalMaterials.FindChecked(MAT);}
			//
			if (FVector::Distance(CLLL_Hit.ImpactPoint,LL_Location)<=CLLL_Material->WaveReflectionRadius) {
				CLLL_PartiallyOccluded = true;
				CLLL_Occluded = false;
			}
		}
		/// Trace 6: CL to LR:
		if ((!CLLR_Occluded) && CL.IsValid() && Listener.Get()->LR.IsValid() && RayTrace(Channel,CLLR_Hit,CL_Location,LR_Location,TraceParameters)) {SPLTCLR += 1; CLLR_Occluded = true;}
		/// Trace 7: CR to Listener:
		if ((!CRLT_Occluded) && CR.IsValid() && RayTrace(Channel,CRLT_Hit,CR_Location,LT_Location,TraceParameters)) {SPLTCLR += 1; CRLT_Occluded = true;}
		/// Trace 8: CR to LL:
		if ((!CRLL_Occluded) && CR.IsValid() && Listener.Get()->LL.IsValid() && RayTrace(Channel,CRLL_Hit,CR_Location,LL_Location,TraceParameters)) {SPLTCLR += 1; CRLL_Occluded = true;}
		/// Trace 9: CR to LR:
		if ((!CRLR_Occluded) && CR.IsValid() && Listener.Get()->LR.IsValid() && RayTrace(Channel,CRLR_Hit,CR_Location,LR_Location,TraceParameters)) {
			CRLR_Occluded = true; SPLTCLR += 1;
			//
			const auto MAT = CRLR_Hit.PhysMaterial.Get();
			if (MAT && Settings->PhysicalMaterials.Find(MAT)) {CRLR_Material = &Settings->PhysicalMaterials.FindChecked(MAT);}
			//
			if (FVector::Distance(CRLR_Hit.ImpactPoint,LR_Location)<=CRLR_Material->WaveReflectionRadius) {
				CRLR_PartiallyOccluded = true;
				CRLR_Occluded = false;
			}
		}
	}
	//
	//
	/// Store Sound Parameters for each Trace:
	float SPLT_LPFFrequency = AT_Defaults.LPFFrequency;
	float SPLL_LPFFrequency = AT_Defaults.LPFFrequency;
	float SPLR_LPFFrequency = AT_Defaults.LPFFrequency;
	float CLLT_LPFFrequency = AT_Defaults.LPFFrequency;
	float CLLL_LPFFrequency = AT_Defaults.LPFFrequency;
	float CLLR_LPFFrequency = AT_Defaults.LPFFrequency;
	float CRLT_LPFFrequency = AT_Defaults.LPFFrequency;
	float CRLL_LPFFrequency = AT_Defaults.LPFFrequency;
	float CRLR_LPFFrequency = AT_Defaults.LPFFrequency;
	float SPLT_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float SPLL_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float SPLR_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CLLT_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CLLL_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CLLR_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CRLT_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CRLL_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float CRLR_PitchMultiplier = AT_Defaults.PitchMultiplier;
	float SPLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float SPLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float SPLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CLLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CLLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CLLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CRLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CRLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	float CRLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	//
	//
	/// Apply SP to LT:
	if (SPLT_PartiallyOccluded) {
		SPLT_LPFFrequency = SPLT_Material->LPFFrequency;
		SPLT_PitchMultiplier = AT_Defaults.PitchMultiplier-0.2f;
		SPLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/10.f;
	} else if (SPLT_Occluded) {
		SPLT_LPFFrequency = SPLT_Material->LPFFrequency/SPLTCLR;
		SPLT_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		SPLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply SP to LL:
	if (SPLL_Occluded) {
		SPLL_LPFFrequency = SPLT_Material->LPFFrequency/SPLTCLR;
		SPLL_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		SPLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply SP to LR:
	if (SPLR_Occluded) {
		SPLR_LPFFrequency = SPLT_Material->LPFFrequency/SPLTCLR;
		SPLR_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		SPLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply CL to LT:
	if (CLLT_Occluded) {
		CLLT_LPFFrequency = CLLL_Material->LPFFrequency/SPLTCLR;
		CLLT_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CLLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply CL to LL:
	if (CLLL_PartiallyOccluded) {
		CLLL_LPFFrequency = CLLL_Material->LPFFrequency;
		CLLL_PitchMultiplier = AT_Defaults.PitchMultiplier-0.1f;
		CLLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/10.f;
	} else if (CLLL_Occluded) {
		CLLL_LPFFrequency = CLLL_Material->LPFFrequency/SPLTCLR;
		CLLL_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CLLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply CL to LR:
	if (CLLR_Occluded) {
		CLLR_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CLLR_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CLLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply CR to LT:
	if (CRLT_Occluded) {
		CRLT_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CRLT_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CRLT_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply CR to LL:
	if (CRLL_Occluded) {
		CRLL_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CRLL_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CRLL_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	/// Apply CR to LR:
	if (CRLR_PartiallyOccluded) {
		CRLR_LPFFrequency = CRLR_Material->LPFFrequency;
		CRLR_PitchMultiplier = AT_Defaults.PitchMultiplier-0.1f;
		CRLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/10.f;
	} else if (CRLR_Occluded) {
		CRLR_LPFFrequency = CRLR_Material->LPFFrequency/SPLTCLR;
		CRLR_PitchMultiplier = AT_Defaults.PitchMultiplier/SPLTCLR;
		CRLR_VolumeMultiplier = AT_Defaults.VolumeMultiplier/SPLTCLR;
	}
	//
	//
	/// Calculate Averages:
	float __LPFFrequency = ((
		SPLT_LPFFrequency + SPLL_LPFFrequency + SPLR_LPFFrequency +
		CLLT_LPFFrequency + CLLL_LPFFrequency + CLLR_LPFFrequency +
		CRLT_LPFFrequency + CRLL_LPFFrequency + CRLR_LPFFrequency
	) / SPLTCLR) - (SPLT_Material->SurfaceTemperature + SPLT_Material->SurfaceDensity);
	//
	float _LPFFrequency = ((
		SPLT_Material->LPFFrequency +
		CLLL_Material->LPFFrequency +
		CRLR_Material->LPFFrequency
	) / SPLT_Material->SurfaceDensity) -
	((
		SPLT_Material->SurfaceDensity +
		CLLL_Material->SurfaceDensity +
		CRLR_Material->SurfaceDensity
	) / (SPLTCLR));
	//
	float _VolumeMultiplier = ((
		SPLT_VolumeMultiplier + SPLL_VolumeMultiplier + SPLR_VolumeMultiplier +
		CLLT_VolumeMultiplier + CLLL_VolumeMultiplier + CLLR_VolumeMultiplier +
		CRLT_VolumeMultiplier + CRLL_VolumeMultiplier + CRLR_VolumeMultiplier
	) / SPLTCLR);
	//
	float _PitchMultiplier = ((
		(SPLT_PitchMultiplier + SPLL_PitchMultiplier + SPLR_PitchMultiplier +
		CLLT_PitchMultiplier + CLLL_PitchMultiplier + CLLR_PitchMultiplier +
		CRLT_PitchMultiplier + CRLL_PitchMultiplier + CRLR_PitchMultiplier)
	) / SPLTCLR);
	//
	//
	AT_Attenuation.PitchMultiplier = FMath::Clamp<float>((_PitchMultiplier/SPLTCLR),AT_Defaults.PitchMultiplier-0.020f,AT_Defaults.PitchMultiplier);
	AT_Attenuation.LPFFrequency = FMath::Clamp<float>(((__LPFFrequency+_LPFFrequency)/2),20.f,20000.f);
	//
	if (SPLT_PartiallyOccluded||CLLL_PartiallyOccluded||CRLR_PartiallyOccluded) {
		AT_Attenuation.VolumeMultiplier = FMath::Clamp<float>(_VolumeMultiplier,0.001f,AT_Defaults.VolumeMultiplier);
		if (AT_Attenuation.VolumeMultiplier<0.001f) {AT_Attenuation.VolumeMultiplier=0.001f;}
	} else {
		AT_Attenuation.VolumeMultiplier = FMath::Clamp<float>((_VolumeMultiplier/SPLTCLR),0.001f,AT_Defaults.VolumeMultiplier);
		if (AT_Attenuation.VolumeMultiplier<0.001f) {AT_Attenuation.VolumeMultiplier=0.001f;}
	}
	//
	//
	SetPitchMultiplier(FMath::Lerp<float>(PitchMultiplier,AT_Attenuation.PitchMultiplier,0.001f));
	SetLowPassFilterFrequency(FMath::Lerp<float>(LowPassFilterFrequency,AT_Attenuation.LPFFrequency,SPLT_Material->WaveSignalInterpolation));
	//
	VolumeMultiplier = FMath::Lerp<float>(VolumeMultiplier,AT_Attenuation.VolumeMultiplier,SPLT_Material->WaveSignalInterpolation);
	if (VolumeMultiplier<0.001f) {VolumeMultiplier=0.001f;} SetVolumeMultiplier(VolumeMultiplier);
	//
	if (AT_Attenuation.VolumeMultiplier > 0.1f){Listener.Get()->OnListenOnce(Listener.Get(),this);}
	if (AT_Attenuation.VolumeMultiplier > 0.1f) {Listener.Get()->OnListen(Listener.Get(),this);}
}

void USPBSO_Speaker::SoundFinished(UAudioComponent* Component) {
	if (!IsActive()||!!HasBegunPlay()) {return;}
	if (NoTrace) {return;}
	//
	ResetSettings();
	Execute_OnSoundFinished(this,this);
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

bool USPBSO_Speaker::RayTrace(const TEnumAsByte<ECollisionChannel> &TraceChannel, FHitResult &Hit, const FVector &Start, const FVector &End, FCollisionQueryParams &Parameters) {
	if (!IsActive()||!HasBegunPlay()||!GetOwner()||!GetOwner()->GetWorld()) {return false;}
	//
	Hit = FHitResult(ForceInit);
	GetOwner()->GetWorld()->LineTraceSingleByChannel(Hit,Start,End,TraceChannel,Parameters);
	//
	return (Hit.GetActor()!=nullptr);
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void USPBSO_Speaker::FetchListeners() {
	if (GetWorld()==nullptr) {return;}
	if (!HasBegunPlay()) {return;}
	//
	for (TActorIterator<AActor>ACT(GetWorld()); ACT; ++ACT) {
		TArray<UActorComponent*>CMPs;
		(*ACT)->GetComponents(USPBSO_Listener::StaticClass(),CMPs);
		//
		for (const auto &LT : CMPs) {
			TWeakObjectPtr<USPBSO_Listener>Listener = Cast<USPBSO_Listener>(LT);
			Listeners.Add(Listener);
		}
	}
}

void USPBSO_Speaker::FetchGates() {
	if (GetWorld()==nullptr) {return;}
	if (!HasBegunPlay()) {return;}
	//
	for (TActorIterator<AActor>ACT(GetWorld()); ACT; ++ACT) {
		TArray<UActorComponent*>CMPs;
		(*ACT)->GetComponents(USPBSO_Gate::StaticClass(),CMPs);
		//
		for (const auto &LT : CMPs) {
			TWeakObjectPtr<USPBSO_Gate>Gate = Cast<USPBSO_Gate>(LT);
			Gates.Add(Gate);
		}
	}
}

void USPBSO_Speaker::FetchSettings() {
	AT_Defaults.LPFFrequency = DefaultLowPassFilter;
	AT_Defaults.PitchMultiplier = DefaultPitchMultiplier;
	AT_Defaults.VolumeMultiplier = DefaultVolumeMultiplier;
}

void USPBSO_Speaker::ResetSettings() {
	bOverrideAttenuation = WantsAttenuationOverride;
	AttenuationSettings = DefaultAttenuation;
	//
	AT_Attenuation.LPFFrequency = AT_Defaults.LPFFrequency;
	AT_Attenuation.PitchMultiplier = AT_Defaults.PitchMultiplier;
	AT_Attenuation.VolumeMultiplier = AT_Defaults.VolumeMultiplier;
	//
	SetPitchMultiplier(AT_Defaults.PitchMultiplier);
	SetVolumeMultiplier(AT_Defaults.VolumeMultiplier);
	SetLowPassFilterFrequency(AT_Defaults.LPFFrequency);
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

void USPBSO_Speaker::Initialize_Implementation() {
	Execute_OnInitialized(this,this);
}

void USPBSO_Speaker::OnSoundPlay_Implementation(const USPBSO_Speaker* Context, const float Percent) {
	for (TObjectIterator<AActor>Actor; Actor; ++Actor) {
		const auto &Interface = Cast<ISPBSO_ISpeaker>(*Actor);
		if (Interface) {Interface->Execute_OnSoundPlay(*Actor,Context,Percent);} else
		if (Actor->GetClass()->ImplementsInterface(USPBSO_ISpeaker::StaticClass())) {
			ISPBSO_ISpeaker::Execute_OnSoundPlay(*Actor,Context,Percent);
	}} EVENT_OnPlay.Broadcast(Percent);
}

void USPBSO_Speaker::OnSoundFinished_Implementation(const USPBSO_Speaker* Context) {
	for (TObjectIterator<AActor>Actor; Actor; ++Actor) {
		const auto &Interface = Cast<ISPBSO_ISpeaker>(*Actor);
		if (Interface) {Interface->Execute_OnSoundFinished(*Actor,Context);} else
		if (Actor->GetClass()->ImplementsInterface(USPBSO_ISpeaker::StaticClass())) {
			ISPBSO_ISpeaker::Execute_OnSoundFinished(*Actor,Context);
	}} EVENT_OnStop.Broadcast();
}

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////