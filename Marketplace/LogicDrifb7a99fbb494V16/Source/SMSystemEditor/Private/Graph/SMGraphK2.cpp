// Copyright Recursoft LLC. All Rights Reserved.

#include "Graph/SMGraphK2.h"

#include "Configuration/SMNodeSettings.h"
#include "Configuration/SMProjectEditorSettings.h"
#include "Construction/ISMEditorConstructionManager.h"
#include "Graph/Nodes/RootNodes/SMGraphK2Node_RuntimeNodeContainer.h"
#include "Graph/Nodes/SMGraphNode_Base.h"
#include "SMSystemEditorLog.h"
#include "Utilities/SMBlueprintEditorUtils.h"

#include "Blueprints/SMBlueprint.h"

USMGraphK2::USMGraphK2(const FObjectInitializer& ObjectInitializer)
	: Super(ObjectInitializer)
{
	if (GIsEditor && !HasAnyFlags(RF_Transient))
	{
		OnCacheClearedHandle = FSMBlueprintEditorUtils::OnCacheClearedEvent.AddUObject(this, &USMGraphK2::OnBlueprintCacheCleared);
	}
}

USMGraphK2::~USMGraphK2()
{
	DECLARE_SCOPE_CYCLE_COUNTER(TEXT("USMGraphK2::~USMGraphK2"), STAT_USMGraphK2Destructor, STATGROUP_LogicDriverEditor);
	
	if (OnCacheClearedHandle.IsValid())
	{
		FSMBlueprintEditorUtils::OnCacheClearedEvent.Remove(OnCacheClearedHandle);
	}
}

bool USMGraphK2::HasAnyLogicConnections() const
{
	if (bHasLogicConnectionsCached.IsSet())
	{
		return bHasLogicConnectionsCached.GetValue();
	}
	
	TArray<USMGraphK2Node_RuntimeNode_Base*> RootNodeList;

	// We want to find the node even if it's buried in a nested graph.
	FSMBlueprintEditorUtils::GetAllNodesOfClassNested<USMGraphK2Node_RuntimeNode_Base>(this, RootNodeList);

	for (const USMGraphK2Node_RuntimeNode_Base* RootNode : RootNodeList)
	{
		if (RootNode->IsConsideredForEntryConnection() && RootNode->GetOutputNode())
		{
			bHasLogicConnectionsCached = true;
			return true;
		}
	}

	bHasLogicConnectionsCached = false;
	return false;
}

void USMGraphK2::ResetCachedValues()
{
	bHasLogicConnectionsCached.Reset();

	TArray<USMGraphK2Node_RuntimeNode_Base*> RootNodeList;
	FSMBlueprintEditorUtils::GetAllNodesOfClassNested<USMGraphK2Node_RuntimeNode_Base>(this, RootNodeList);
	
	for (USMGraphK2Node_RuntimeNode_Base* RootNode : RootNodeList)
	{
		RootNode->ResetCachedValues();
	}
}

void USMGraphK2::PostRename(UObject* OldOuter, const FName OldName)
{
	Super::PostRename(OldOuter, OldName);

	if (USMGraphNode_Base* GraphNode = Cast<USMGraphNode_Base>(GetOuter()))
	{
		GraphNode->OnBoundGraphRenamed(OldOuter, OldName);
	}
	
	const ESMEditorConstructionScriptProjectSetting ConstructionProjectSetting = FSMBlueprintEditorUtils::GetProjectEditorSettings()->EditorNodeConstructionScriptSetting;
	if (ConstructionProjectSetting == ESMEditorConstructionScriptProjectSetting::SM_Standard)
	{
		// Find correct blueprint to run cs for.
		USMBlueprint* Blueprint = FSMBlueprintEditorUtils::FindBlueprintFromObject(this);
		if (!Blueprint)
		{
			// If this graph is being deleted the new outer would be the transient package, try to find old package.
			Blueprint = FSMBlueprintEditorUtils::FindBlueprintFromObject(OldOuter);
		}
		if (Blueprint)
		{
			ISMEditorConstructionManager::Get().RunAllConstructionScriptsForBlueprint(Blueprint);
		}
	}
}

void USMGraphK2::NotifyGraphChanged()
{
	Super::NotifyGraphChanged();
	
	if (UBlueprint* Blueprint = FSMBlueprintEditorUtils::FindBlueprintForGraph(this))
	{
		if (!Blueprint->bBeingCompiled && !Blueprint->HasAnyFlags(RF_NeedLoad | RF_NeedPostLoad))
		{
			FSMBlueprintEditorUtils::FixUpAutoGeneratedFunctions(Blueprint, true);
		}
	}
}

void USMGraphK2::NotifyGraphChanged(const FEdGraphEditAction& Action)
{
	Super::NotifyGraphChanged(Action);
}

void USMGraphK2::OnBlueprintCacheCleared(const USMBlueprint* Blueprint)
{
	if (Blueprint == FBlueprintEditorUtils::FindBlueprintForGraph(this))
	{
		ResetCachedValues();
	}
}
