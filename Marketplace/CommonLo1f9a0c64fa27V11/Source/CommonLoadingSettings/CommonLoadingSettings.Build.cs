// Copyright (C) 2017-2023 eelDev AB

using UnrealBuildTool;

public class CommonLoadingSettings : ModuleRules
{
	public CommonLoadingSettings(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		//PrivatePCHHeaderFile = "Private/CommonLoadingSettingsPrivatePCH.h";

		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
			}
		);


		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"CoreUObject",
				"Engine",
				"Slate",
				"SlateCore",
				"InputCore",
				"PreLoadScreen",
				"RenderCore",
				"DeveloperSettings",
				"UMG",
				"Projects"
			}
		);
	}
}
