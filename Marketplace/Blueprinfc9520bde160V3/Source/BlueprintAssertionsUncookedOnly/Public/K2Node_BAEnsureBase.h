// Copyright 2025 Just2Devs. All Rights Reserved.

#pragma once
#include "CoreMinimal.h"
#include "K2Node.h"
#include "K2Node_BABase.h"
#include "K2Node_BAEnsureBase.generated.h"

UCLASS(Abstract)
class BLUEPRINTASSERTIONSUNCOOKEDONLY_API UK2Node_BAEnsureBase : public UK2Node_BABase
{
	GENERATED_BODY()

public:
	UK2Node_BAEnsureBase();
	
public:
	virtual bool CanPlaceBreakpoints() const override;
	virtual FLinearColor GetNodeTitleColor() const override;

	UFUNCTION()
	bool GetHit() const { return bHit; }
	
	UFUNCTION()
	void SetHit(const bool bNewHit) { bHit = bNewHit; }

	UFUNCTION()
	int32 GetHitCount() const { return HitCount; }
	
	UFUNCTION()
	void IncreaseCount() { HitCount++; }

protected:
	bool bOpenMessageLogOnEndPie = true;
	
private:
	bool bHit = false;
	int32 HitCount = 0;
	
private:
	void OnPreBeginPIE(bool bSimulating);
	void OnEndPIE(bool bSimulating);
	
};
