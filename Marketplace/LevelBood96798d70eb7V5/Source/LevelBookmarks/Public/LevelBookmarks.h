// Copyright 2025 Ares9323. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"

UENUM()
enum class EGameMapToSet : uint8
{
	EditorStartupMap,
	GameDefaultMap,
	TransitionMap
};

class FLevelBookmarksModule : public IModuleInterface
{
public:
	/** IModuleInterface implementation */
	virtual void StartupModule() override;
	virtual void ShutdownModule() override;

private:
	void ExtendContextMenu();
	void AddLevelBookmarksFromContextMenu();
	FDelegateHandle ToolMenusHandle;

};