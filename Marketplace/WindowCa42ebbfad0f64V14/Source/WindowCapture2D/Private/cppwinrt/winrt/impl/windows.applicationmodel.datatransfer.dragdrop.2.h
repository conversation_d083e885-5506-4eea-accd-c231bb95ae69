// C++/WinRT v2.0.220110.5

// Copyright (c) Microsoft Corporation. All rights reserved.
// Licensed under the MIT License.

#pragma once
#ifndef WINRT_Windows_ApplicationModel_DataTransfer_DragDrop_2_H
#define WINRT_Windows_ApplicationModel_DataTransfer_DragDrop_2_H
#include "winrt/impl/Windows.ApplicationModel.DataTransfer.DragDrop.1.h"
WINRT_EXPORT namespace winrt::Windows::ApplicationModel::DataTransfer::DragDrop
{
}
#endif
