// Copyright 2024 Black Eye Technologies, Ltd. All Rights Reserved.


#include "Actors/BlackEyeSimpleLookAt.h"


ABlackEyeSimpleLookAt::ABlackEyeSimpleLookAt()
{
    Follow = CreateDefaultSubobject<UFollowComponent>(TEXT("Follow"));
    SetRootComponent(Follow);

    LookAt = CreateDefaultSubobject<ULookAtComponent>(TEXT("LookAt"));
    LookAt->AttachToComponent(Follow, FAttachmentTransformRules::KeepRelativeTransform);
}

ABlackEyeSimpleLookAt::ABlackEyeSimpleLookAt(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    Follow = ObjectInitializer.CreateDefaultSubobject<UFollowComponent>(this, TEXT("Follow"));
    SetRootComponent(Follow);

    LookAt = ObjectInitializer.CreateDefaultSubobject<ULookAtComponent>(this, TEXT("LookAt"));
    LookAt->AttachToComponent(Follow, FAttachmentTransformRules::KeepRelativeTransform);
}