// Copyright 2024, <PERSON>, All rights reserved.

#pragma once

#include "CoreMinimal.h"
#include "AdvancedSightTargetComponent.h"
#include "Components/ActorComponent.h"
#include "AdvancedSightTargetState.h"
#include "AdvancedSightComponent.generated.h"

struct FAdvancedSightTargetGainValuePair;

class APawn;
class UAdvancedSightSystem;
class UAdvancedSightData;
class UMeshComponent;

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAdvancedSightComponentDelegate, AActor*, TargetActor);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(
	FAdvancedSightComponentLoseTargetDelegate, AActor*, TargetActor, bool, bWasPerceived);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(
	FAdvancedSightComponentPerceiveTargetDelegate, AActor*, TargetActor, bool, bWasRemembered);

// This component should be attached to a controller, not a pawn.
UCLASS(ClassGroup=(AI), meta=(BlueprintSpawnableComponent))
class ADVANCEDSIGHT_API UAdvancedSightComponent : public UActorComponent
{
	GENERATED_BODY()

public:
	UAdvancedSightComponent();
	virtual void BeginPlay() override;
	virtual void EndPlay(const EEndPlayReason::Type EndPlayReason) override;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void Enable();

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void Disable();

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsEnabled() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	UAdvancedSightData* GetSightData() const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void OverrideSightData(UAdvancedSightData* NewSightData);

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	FTransform GetEyePointOfViewTransform() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	AActor* GetBodyActor() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsTargetPerceived(const AActor* TargetActor) const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsTargetSpotted(const AActor* TargetActor) const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsTargetRemembered(const AActor* TargetActor) const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	EAdvancedSightTargetState GetStateForTarget(const AActor* TargetActor) const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	float GetGainValueForTarget(const AActor* TargetActor) const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	FAdvancedSightTargetGainValuePair GetHighestGainValue() const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	const TArray<AActor*>& GetPerceivedTargets() const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	const TArray<AActor*>& GetSpottedTargets() const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	const TArray<AActor*>& GetRememberedTargets() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsAnyTargetPerceived() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsAnyTargetSpotted() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	bool IsAnyTargetRemembered() const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void ForceForgetTarget(AActor* TargetActor);

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void ForceForgetAllTargets();

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void ForcePerceiveTarget(AActor* TargetActor);

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void AddGainToTarget(AActor* TargetActor, float Value);

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	float GetGainRateMultiplier() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	float GetGainDecayMultiplier() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	float GetWaitTimeMultiplier() const;

	UFUNCTION(BlueprintPure, Category = "Advanced Sight")
	float GetForgetTimeMultiplier() const;

	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void SetGainRateMultiplier(float NewGainRateMultiplier);
	
	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void SetGainDecayMultiplier(float NewGainDecayMultiplier);
	
	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void SetWaitTimeMultiplier(float NewWaitTimeMultiplier);
	
	UFUNCTION(BlueprintCallable, Category = "Advanced Sight")
	void SetForgetTimeMultiplier(float NewForgetTimeMultiplier);
	
	void SpotTarget(AActor* TargetActor);
	void PerceiveTarget(AActor* TargetActor, bool bWasRemembered);
	void StartTargetLostWait(AActor* TargetActor);
	void LoseTarget(AActor* TargetActor, bool bWasActorPerceived);
	void ForgetTarget(AActor* TargetActor);

	// This delegate is called whenever a new, valid target becomes visible to the owner.
	UPROPERTY(BlueprintAssignable)
	FAdvancedSightComponentDelegate OnTargetSpotted;

	// This delegate is called whenever a target was spotted and visible for long enough or
	// when a target was lost, but not yet forgotten, and becomes visible again.
	UPROPERTY(BlueprintAssignable)
	FAdvancedSightComponentPerceiveTargetDelegate OnTargetPerceived;

	// This delegate is called immediately after either spotted or perceived target is no longer visible.
	UPROPERTY(BlueprintAssignable)
	FAdvancedSightComponentLoseTargetDelegate OnTargetLost;

	// This delegate is called when a target was perceived, but was not visible for long enough and is now
	// forgotten by the owner.
	UPROPERTY(BlueprintAssignable)
	FAdvancedSightComponentDelegate OnTargetForgotten;

	UPROPERTY(BlueprintAssignable)
	FAdvancedSightComponentDelegate OnTargetLostWaitTimeStarted;
protected:
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Advanced Sight")
	TObjectPtr<UAdvancedSightData> SightData;

	// If None will use default Pawn transform. Otherwise, will use defined socket's transform.
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Advanced Sight")
	FName EyeSocketName = NAME_None;

	// If set to true, the system will only use Yaw value of the socket's transform rotation. Otherwise, it will use
	// actual socket's transform without ignoring any of the components.
	UPROPERTY(EditDefaultsOnly, BlueprintReadOnly, Category = "Advanced Sight")
	bool bUseOnlyYawFromEyeSocket = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Advanced Sight")
	bool bShouldStartEnabled = true;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Advanced Sight")
	float GainRateMultiplier = 1.0f;
	
	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Advanced Sight")
	float GainDecayRateMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Advanced Sight")
	float WaitTimeMultiplier = 1.0f;

	UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Advanced Sight")
	float ForgetTimeMultiplier = 1.0f;

	UPROPERTY(Transient)
	TArray<TObjectPtr<AActor>> PerceivedTargets;

	UPROPERTY(Transient)
	TArray<TObjectPtr<AActor>> SpottedTargets;

	UPROPERTY(Transient)
	TArray<TObjectPtr<AActor>> RememberedTargets;

	TWeakObjectPtr<UAdvancedSightSystem> AdvancedSightSystem;
	mutable TWeakObjectPtr<const UMeshComponent> CachedMeshComponent;
	bool bIsEnabled = false;

#if WITH_EDITOR
	void HandleSightDataUpdated(const UAdvancedSightData* AdvancedSightData);
#endif

	UFUNCTION()
	void HandlePawnPossessed(APawn* OldPawn, APawn* NewPawn);
};
