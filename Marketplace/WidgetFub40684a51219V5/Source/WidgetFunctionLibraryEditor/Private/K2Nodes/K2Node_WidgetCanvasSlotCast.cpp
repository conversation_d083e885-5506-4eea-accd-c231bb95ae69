// Copyright 2022 Just2Devs. All Rights Reserved.

#include "K2Node_WidgetCanvasSlotCast.h"
#include "WidgetFL.h"
#include "Components/CanvasPanelSlot.h"

void UK2Node_WidgetCanvasSlotCast::ExpandNode(FKismetCompilerContext& CompilerContext, UEdGraph* SourceGraph)
{
	FunctionName = GET_MEMBER_NAME_CHECKED(UWidgetFL, CastCanvasSlot);
	Super::ExpandNode(CompilerContext, SourceGraph);
}

void UK2Node_WidgetCanvasSlotCast::AllocateDefaultPins()
{
	PinSlotType = UCanvasPanelSlot::StaticClass();
	Super::AllocateDefaultPins();
}
