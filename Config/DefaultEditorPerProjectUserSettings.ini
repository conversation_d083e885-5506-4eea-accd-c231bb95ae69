[/Script/SmartBlueprintFunctionCreator.SmartBlueprintCreatorSettings]
ActivationChord=(Key=LeftMouseButton,bShift=True,bCtrl=False,bAlt=False,bCmd=False)
DefaultCreationType=Event
+CommandKeywordAliases=(Keyword="event",Aliases=("e"))
+CommandKeywordAliases=(Keyword="func",Aliases=("fn"))
+CommandKeywordAliases=(Keyword="server",Aliases=("s"))
+CommandKeywordAliases=(Keyword="client",Aliases=("c"))
+CommandKeywordAliases=(Keyword="multicast",Aliases=("m"))
+CommandKeywordAliases=(Keyword="reliable",Aliases=("rel"))
+CommandKeywordAliases=(Keyword="pure",Aliases=("p"))
+CommandKeywordAliases=(Keyword="const",Aliases=("k"))
+DataTypeAliases=(Keyword="boolean",Aliases=("b","bool","boolean"))
+DataTypeAliases=(Keyword="byte",Aliases=("u8","byte"))
+DataTypeAliases=(Keyword="integer",Aliases=("i","int","integer"))
+DataTypeAliases=(Keyword="integer64",Aliases=("i64","long","integer64"))
+DataTypeAliases=(Keyword="float",Aliases=("f","float","double"))
+DataTypeAliases=(Keyword="string",Aliases=("str","string"))
+DataTypeAliases=(Keyword="name",Aliases=("n","name"))
+DataTypeAliases=(Keyword="text",Aliases=("txt","text"))
+DataTypeAliases=(Keyword="vector",Aliases=("v","vec","vec3","vector"))
+DataTypeAliases=(Keyword="rotator",Aliases=("r","rot","rotator"))
+DataTypeAliases=(Keyword="transform",Aliases=("t","xform","transform"))
bShowUsageTips=True

