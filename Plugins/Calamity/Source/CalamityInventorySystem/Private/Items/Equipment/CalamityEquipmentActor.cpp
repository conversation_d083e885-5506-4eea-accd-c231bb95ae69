#include "Items/Equipment/CalamityEquipmentActor.h"
#include "Items/Equipment/CalamityEquipmentInstance.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/SceneComponent.h"
#include "Components/SphereComponent.h"
#include "Components/WidgetComponent.h"

ACalamityEquipmentActor::ACalamityEquipmentActor()
{
	PrimaryActorTick.bCanEverTick = false;
	bReplicates = true;
	
	RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
	RootComponent = RootSceneComponent;
	
	// Active mesh components (visible when equipped/in hands)
	SkeletalMeshComponent = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("SkeletalMeshComponent"));
	SkeletalMeshComponent->SetupAttachment(RootComponent);
	
	StaticMeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("StaticMeshComponent"));
	StaticMeshComponent->SetupAttachment(RootComponent);
	
	// NonActive mesh components (visible when holstered/stowed)
	NonActiveSkeletalMeshComponent = CreateDefaultSubobject<USkeletalMeshComponent>(TEXT("NonActiveSkeletalMeshComponent"));
	NonActiveSkeletalMeshComponent->SetupAttachment(RootComponent);
	NonActiveSkeletalMeshComponent->SetVisibility(false);
	
	NonActiveStaticMeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("NonActiveStaticMeshComponent"));
	NonActiveStaticMeshComponent->SetupAttachment(RootComponent);
	NonActiveStaticMeshComponent->SetVisibility(false);

    ACalamityEquipmentActor::SetupMeshComponents();
    
    PickupWidget = CreateDefaultSubobject<UWidgetComponent>(TEXT("PickupWidget"));
    PickupWidget->SetVisibility(false);
	PickupWidget->SetupAttachment(RootComponent);

    OverlapSphere = CreateDefaultSubobject<USphereComponent>(TEXT("OverlapSphere"));
    OverlapSphere->SetupAttachment(RootComponent);
}

void ACalamityEquipmentActor::Initialize(UCalamityEquipmentInstance* EquipmentInstance, UCalamityEquipmentFragment* EquipmentFragment, bool bInPooled)
{
    InitializeFragment(EquipmentFragment);
    BindEquipment(EquipmentInstance);
    SetPooled(bInPooled);
}

void ACalamityEquipmentActor::InitializeFragment(UCalamityEquipmentFragment* EquipmentFragment)
{
    // Default no-op; subclasses may override for additional setup
}

void ACalamityEquipmentActor::BindEquipment(UCalamityEquipmentInstance* Equipment)
{
    BoundEquipment = Equipment;
    OnBound();
    BP_OnBound();
}

void ACalamityEquipmentActor::UnbindEquipment()
{
    OnUnbound();
    BP_OnUnbound();
    BoundEquipment = nullptr;
}

void ACalamityEquipmentActor::ActivateEquipment()
{
    bIsActive = true;
    SetActiveMeshVisibility(true);
    OnActivated();
    SetActorTickEnabled(true);
}

void ACalamityEquipmentActor::DeactivateEquipment()
{
    bIsActive = false;
    SetActiveMeshVisibility(false);
    OnDeactivated();
    SetActorTickEnabled(false);
}

UMeshComponent* ACalamityEquipmentActor::GetMesh() const
{
    if (bIsActive)
    {
        // Return active mesh (prefer skeletal over static)
        if (SkeletalMeshComponent && SkeletalMeshComponent->GetSkeletalMeshAsset())
        {
            return SkeletalMeshComponent;
        }
        return StaticMeshComponent;
    }
    if (NonActiveSkeletalMeshComponent && NonActiveSkeletalMeshComponent->GetSkeletalMeshAsset())
    {
        // Return non-active mesh (prefer skeletal over static)
        if (NonActiveSkeletalMeshComponent && NonActiveSkeletalMeshComponent->GetSkeletalMeshAsset())
        {
            return NonActiveSkeletalMeshComponent;
        }
        return NonActiveStaticMeshComponent;
    }
    return nullptr;
}

UMeshComponent* ACalamityEquipmentActor::GetNonActiveMesh() const
{
    if (NonActiveSkeletalMeshComponent && NonActiveSkeletalMeshComponent->GetSkeletalMeshAsset())
    {
        return NonActiveSkeletalMeshComponent;
    }
    if (NonActiveStaticMeshComponent && NonActiveStaticMeshComponent->GetStaticMesh())
    {
        return NonActiveStaticMeshComponent;
    }
    return nullptr;
}

void ACalamityEquipmentActor::SetActiveMeshVisibility(bool bShowActive)
{
    if (bShowActive)
    {
        // Show active meshes, hide non-active meshes
        SkeletalMeshComponent->SetVisibility(true);
        StaticMeshComponent->SetVisibility(true);
        NonActiveSkeletalMeshComponent->SetVisibility(false);
        NonActiveStaticMeshComponent->SetVisibility(false);
    }
    else
    {
        // Hide active meshes, show non-active meshes (if configured)
        SkeletalMeshComponent->SetVisibility(false);
        StaticMeshComponent->SetVisibility(false);
        
        if (bHasSeparateNonActiveMesh)
        {
            NonActiveSkeletalMeshComponent->SetVisibility(true);
            NonActiveStaticMeshComponent->SetVisibility(true);
        }
        else
        {
            // Use the same mesh but in non-active position
            SkeletalMeshComponent->SetVisibility(true);
            StaticMeshComponent->SetVisibility(true);
        }
    }
}

void ACalamityEquipmentActor::SetupNonActiveMesh()
{
    if (!bHasSeparateNonActiveMesh)
    {
        return;
    }

    // Setup non-active skeletal mesh
    if (NonActiveSkeletalMesh)
    {
        if (USkeletalMesh* LoadedSkelMesh = NonActiveSkeletalMesh.LoadSynchronous())
        {
            NonActiveSkeletalMeshComponent->SetSkeletalMesh(LoadedSkelMesh);
        }
    }

    // Setup non-active static mesh
    if (NonActiveStaticMesh)
    {
        if (UStaticMesh* LoadedStaticMesh = NonActiveStaticMesh.LoadSynchronous())
        {
            NonActiveStaticMeshComponent->SetStaticMesh(LoadedStaticMesh);
        }
    }

    // Apply non-active materials
    if (NonActiveMaterials.Num() > 0)
    {
        UMeshComponent* NonActiveMesh = NonActiveSkeletalMeshComponent->GetSkeletalMeshAsset() ? 
            static_cast<UMeshComponent*>(NonActiveSkeletalMeshComponent) : 
            static_cast<UMeshComponent*>(NonActiveStaticMeshComponent);
        
        ApplyMaterials(NonActiveMesh, NonActiveMaterials);
    }
}

void ACalamityEquipmentActor::OnBound()
{
    // Setup non-active mesh when equipment is bound
    SetupNonActiveMesh();
}

void ACalamityEquipmentActor::OnUnbound()
{
    // Default no-op; subclasses may override
}

void ACalamityEquipmentActor::OnActivated()
{
    // Default no-op; subclasses may override
}

void ACalamityEquipmentActor::OnDeactivated()
{
    // Default no-op; subclasses may override
}

void ACalamityEquipmentActor::SetupMeshComponents()
{
    // Default setup - subclasses may override for specific configuration
    // Initially hide static mesh components - they'll be shown when needed
    StaticMeshComponent->SetVisibility(false);
    NonActiveStaticMeshComponent->SetVisibility(false);
}

void ACalamityEquipmentActor::ApplyMaterials(UMeshComponent* MeshComponent, const TArray<TSoftObjectPtr<UMaterialInterface>>& Materials)
{
    if (!MeshComponent || Materials.Num() == 0)
    {
        return;
    }

    for (int32 i = 0; i < Materials.Num(); i++)
    {
        if (Materials[i])
        {
            if (UMaterialInterface* Material = Materials[i].LoadSynchronous())
            {
                MeshComponent->SetMaterial(i, Material);
            }
        }
    }
}
